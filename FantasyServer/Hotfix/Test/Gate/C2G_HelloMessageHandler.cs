
using Fantasy;
using Fantasy.Async;
using Fantasy.Network;
using Fantasy.Network.Interface;
using Fantasy.Platform.Net;

namespace Demo
{
    public class C2G_HelloMessageHandler : Message<C2G_HelloMessage>
    {
        protected override async FTask Run(Session session, C2G_HelloMessage message)
        {
            Console.WriteLine($"Gate received client message: {message.Tag}");
            session.Send(new G2C_HelloMessage(){Tag = "Message, hello client!"});
            
            // 注册中转路由
            var routeComponent = session.AddComponent<RouteComponent>();
            
            var chatSceneConfigs = SceneConfigData.Instance.GetSceneBySceneType(SceneType.Chat);
            var chatSceneConfig = chatSceneConfigs[0];
            session.Scene.NetworkMessagingComponent.SendInnerRoute(chatSceneConfig.RouteId, new G2Chat_HelloMessage(){Tag = "Message, hello chat!"});
            var resp = await session.Scene.NetworkMessagingComponent.CallInnerRoute(chatSceneConfig.RouteId, new G2Chat_HelloRequest(){Tag = "Request, hello chat!"}) as Chat2G_HelloResponse;
            Console.WriteLine(resp != null ? $"Gate received chat response: {resp.Tag} {resp.RouteId}" : "Response is null");
            // 注册聊天中转
            routeComponent.AddAddress(RouteType.ChatRoute, chatSceneConfig.RouteId);
            
            await FTask.CompletedTask;
        }
    }
}
