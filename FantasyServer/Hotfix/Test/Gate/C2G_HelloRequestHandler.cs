using Fantasy;
using Fantasy.Async;
using Fantasy.Network;
using Fantasy.Network.Interface;

namespace Demo
{
    public class C2G_HelloRequestHandler : MessageRPC<C2G_HelloRequest, G2C_HelloResponse>
    {
        protected override async FTask Run(Session session, C2G_HelloRequest request, G2C_HelloResponse response, Action reply)
        {
            Console.WriteLine($"Gate received client request: {request.Tag}");
            response.Tag = $"Gate response to: {request.Tag}";
            await FTask.CompletedTask;
        }
    }
}
