using ProtoBuf;

using System.Collections.Generic;
using MongoDB.Bson.Serialization.Attributes;
using Fantasy;
using Fantasy.Network.Interface;
using Fantasy.Serialize;
// <PERSON>S<PERSON>per disable InconsistentNaming
// ReSharper disable RedundantUsingDirective
// ReSharper disable RedundantOverriddenMember
// ReS<PERSON>per disable PartialTypeWithSinglePart
// ReSharper disable UnusedAutoPropertyAccessor.Global
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable CheckNamespace
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
#pragma warning disable CS8618

namespace Fantasy
{	
	[ProtoContract]
	public partial class G2Chat_HelloMessage : AMessage, IRouteMessage, IProto
	{
		public static G2Chat_HelloMessage Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<G2Chat_HelloMessage>();
		}
		public override void Dispose()
		{
			Tag = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<G2Chat_HelloMessage>(this);
#endif
		}
		public uint OpCode() { return InnerOpcode.G2Chat_HelloMessage; }
		[ProtoMember(1)]
		public string Tag { get; set; }
	}
	[ProtoContract]
	public partial class G2Chat_HelloRequest : AMessage, IRouteRequest, IProto
	{
		public static G2Chat_HelloRequest Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<G2Chat_HelloRequest>();
		}
		public override void Dispose()
		{
			Tag = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<G2Chat_HelloRequest>(this);
#endif
		}
		[ProtoIgnore]
		public Chat2G_HelloResponse ResponseType { get; set; }
		public uint OpCode() { return InnerOpcode.G2Chat_HelloRequest; }
		[ProtoMember(1)]
		public string Tag { get; set; }
	}
	[ProtoContract]
	public partial class Chat2G_HelloResponse : AMessage, IRouteResponse, IProto
	{
		public static Chat2G_HelloResponse Create(Scene scene)
		{
			return scene.MessagePoolComponent.Rent<Chat2G_HelloResponse>();
		}
		public override void Dispose()
		{
			ErrorCode = default;
			Tag = default;
			RouteId = default;
#if FANTASY_NET || FANTASY_UNITY
			GetScene().MessagePoolComponent.Return<Chat2G_HelloResponse>(this);
#endif
		}
		public uint OpCode() { return InnerOpcode.Chat2G_HelloResponse; }
		[ProtoMember(1)]
		public string Tag { get; set; }
		[ProtoMember(2)]
		public long RouteId { get; set; }
		[ProtoMember(3)]
		public uint ErrorCode { get; set; }
	}
}
